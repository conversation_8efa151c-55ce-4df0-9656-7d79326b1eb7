'use client';

import { But<PERSON>, Icon } from '@/src/app/_components';
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { useAnalyticsEventGeneric, useHandleAddToCart } from '@/src/app/_hooks';
import { ServiceType } from '@/src/app/_interfaces';
import { convertToServiceTypes, formatPrice } from '@/src/app/_utils';
import { Calendar, ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

// Global Window interface is defined in global.d.ts

interface MainCardProps {
  service: ServiceType;
}

// Utility function to sanitize image URLs by removing escape characters
function sanitizeImageUrl(url: string): string {
  if (!url) return '';

  // Replace any escape sequences and normalize the URL
  return url.replace(/[\r\n\t\f\v]/g, '').trim();
}

export default function MainCard({ service }: MainCardProps) {
  const router = useRouter();
  const { handleAddToCart } = useHandleAddToCart();
  const { sendEvent } = useAnalyticsEventGeneric();
  const { services: contextServices } = useServiceContext();

  // Initialize all state variables once, at the top level
  const [services, setServices] = useState<ServiceType[]>([]);
  const [selectedService, setSelectedService] = useState<ServiceType | null>(service);
  const [isLoading, setIsLoading] = useState(!service);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isSelectOpen, setIsSelectOpen] = useState(false);

  // Create refs once, at the top level
  const selectRef = useRef<HTMLSelectElement>(null);

  // Effect for updating text display when service changes
  useEffect(() => {
    if (selectedService) {
      setIsLoading(false);
    }
  }, [selectedService]);

  // Effect for select dropdown click handling
  useEffect(() => {
    const handleSelectClick = (e: MouseEvent) => {
      if (selectRef.current && selectRef.current.contains(e.target as Node)) {
        setIsSelectOpen(!isSelectOpen);
      } else if (isSelectOpen) {
        setIsSelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleSelectClick);
    return () => {
      document.removeEventListener('mousedown', handleSelectClick);
    };
  }, [isSelectOpen]);

  useEffect(() => {
    // This effect populates the 'services' list for the dropdown
    // and initializes or updates selectedService based on the 'service' prop.
    if (service && contextServices && Array.isArray(contextServices)) {
      let subcategoryServicesList: ServiceType[] = [];
      let currentServiceFromPropFull: ServiceType | null = null;

      for (const category of contextServices) {
        for (const subcategory of category.subcategories) {
          const serviceInSubcategoryMatch = subcategory.services.find(
            (s) => s.slug === service.slug
          );
          if (serviceInSubcategoryMatch) {
            const mappedServices = subcategory.services.map((s) => ({
              ...s,
              categoryName: category.name,
              categorySlug: category.slug,
              subcategoryName: subcategory.name,
              subcategorySlug: subcategory.slug,
            }));
            subcategoryServicesList = convertToServiceTypes(mappedServices);
            // Find the full object for the current service prop from the generated list
            currentServiceFromPropFull =
              subcategoryServicesList.find((s) => s.slug === service.slug) || null;
            break; // Found subcategory
          }
        }
        if (currentServiceFromPropFull) break; // Found category and service
      }

      setServices(subcategoryServicesList);

      // Initialize selectedService if it's not set OR if the service prop slug
      // has changed and is different from the currently selected service slug.
      if (currentServiceFromPropFull) {
        if (!selectedService || service.slug !== selectedService.slug) {
          setSelectedService(currentServiceFromPropFull);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [service, contextServices]);

  // Handle service selection from dropdown
  const handleServiceChange = async (slug: string) => {
    // Set loading states immediately for faster visual feedback
    setIsLoading(true);
    setImageLoaded(false);

    const newService = services.find((s) => s.slug === slug);
    if (newService) {
      // Track the selection in analytics
      sendEvent(`service_select_${newService.slug}`);

      // Update the selected service state
      setSelectedService(newService);

      // Update URL without full page navigation
      const currentUrl = new URL(window.location.href);
      const newParams = new URLSearchParams();

      // Set the service slug as an empty key parameter
      newParams.append('', slug);

      // Preserve UTM parameters
      for (const [key, value] of currentUrl.searchParams.entries()) {
        if (key.startsWith('utm_')) {
          newParams.append(key, value);
        }
      }

      // Build the new URL with query parameters
      const queryString = newParams.toString();
      const newPath = `${window.location.pathname}${queryString ? `?${queryString}` : ''}`;

      // Replace current history state to avoid adding new entries
      router.replace(newPath, { scroll: false });
    }
  };

  if (!selectedService) return null;

  return (
    <section
      className="overflow-hidden rounded-md bg-white text-slate-800 shadow-none md:shadow-lg"
      aria-labelledby={`service-title-${selectedService.id}`}
      aria-describedby={`service-description-${selectedService.id}`}
      tabIndex={0}
    >
      <div className="flex flex-col-reverse items-stretch lg:flex-row">
        <div className="mb-4 p-6 md:p-8 lg:mb-0 lg:w-1/2 lg:p-12">
          {isLoading ? (
            <div className="mb-8 space-y-3">
              <div className="h-4 w-1/4 animate-pulse rounded bg-gray-200" />
              <div className="h-8 w-1/3 animate-pulse rounded bg-gray-300" />
            </div>
          ) : (
            <div>
              {selectedService.categoryName !== selectedService.subcategoryName && (
                <h2
                  id={`service-title-${service.id}`}
                  className="text-md sm:text-md font-bold leading-tight text-muted-foreground md:text-xl lg:text-2xl"
                >
                  {selectedService.categoryName}
                </h2>
              )}

              <h1
                id={`service-title-${service.id}`}
                className="mb-4 text-2xl font-bold leading-tight sm:text-3xl md:text-4xl lg:text-5xl"
              >
                {selectedService.subcategoryName}
              </h1>
            </div>
          )}

          {/* ✅ Select de serviços (apenas se houver mais de 1 serviço) */}
          {services.length > 1 && (
            <div className="mb-4">
              <label
                htmlFor="service-select"
                className="mb-4 mt-6 block font-bold text-muted-foreground"
              >
                Qual serviço você precisa?
              </label>
              <div className="relative">
                <select
                  ref={selectRef}
                  id="service-select"
                  value={selectedService.slug}
                  onChange={(e) => {
                    handleServiceChange(e.target.value);
                    // Set select as closed after selection
                    setIsSelectOpen(false);
                  }}
                  className="w-full appearance-none text-ellipsis rounded-[8px] border border-gray-300 bg-white px-4 py-3 pr-10 text-gray-900 shadow-sm focus:border-gray-500 focus:ring-gray-500"
                  disabled={isLoading}
                >
                  {/* Sort services alphabetically by name */}
                  {[...services]
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .map((s) => (
                      <option key={s.id} value={s.slug} title={s.name}>
                        {s.name}
                      </option>
                    ))}
                </select>

                {/* ✅ Ícone Lucide */}
                <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 bg-white text-gray-400">
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${isSelectOpen ? 'rotate-180' : ''}`}
                    strokeWidth={3}
                  />
                </span>
              </div>
            </div>
          )}

          {/* Service Description and Pricing Section */}
          <div className="mb-8 rounded-xl bg-[#F8FAFC] p-4 shadow-sm" tabIndex={0}>
            <h3 className="pb-2 text-lg font-bold">{selectedService.name}</h3>
            {isLoading ? (
              <div className="space-y-5">
                <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
                <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
                <div className="h-6 w-1/4 animate-pulse rounded bg-gray-200" />
                <div className="h-8 w-1/3 animate-pulse rounded bg-gray-300" />
              </div>
            ) : (
              <>
                {/* Service Description */}
                <div className="mb-8">
                  <p
                    id={`service-description-${service.id}`}
                    className="text-lg font-medium leading-7 text-[#62748E]"
                  >
                    {selectedService.description}
                  </p>
                </div>

                {/* Pricing Section */}
                <div className="mb-8 w-[245px]">
                  <div className="mb-1 flex items-center gap-2">
                    <p className="text-base font-normal leading-6 text-[#62748E] line-through">
                      {formatPrice(selectedService.price.originalPrice, 'BRL')}
                    </p>
                  </div>
                  <p className="text-2xl font-extrabold leading-8 text-[#020618]" tabIndex={0}>
                    {formatPrice(selectedService.price.finalPrice, 'BRL')}
                  </p>
                </div>

                {/* City Availability and Warning Section */}
                <div className="flex flex-col gap-1">
                  {/* City availability card */}
                  <div className="flex items-center gap-3 rounded-lg bg-[#F1F5F9] p-2">
                    <div className="flex h-5 w-5 items-center justify-center">
                      <Icon name="MapPin" className="h-5 w-5 text-[#62748E]" />
                    </div>
                    <p className="text-sm font-medium leading-5 text-[#62748E]">
                      Disponível em São Paulo, Rio de Janeiro e Minas Gerais.
                    </p>
                  </div>

                  {/* Warning alert box */}
                  <div className="flex items-center gap-3 rounded-lg bg-[#F1F5F9] p-2">
                    <div className="flex h-5 w-5 items-center justify-center">
                      <Icon name="AlertCircle" className="h-5 w-5 text-[#62748E]" />
                    </div>
                    <p className="text-sm font-medium leading-5 text-[#62748E]">
                      A eventual aquisição das peças é de responsabilidade do cliente, não inclusas
                      no valor.
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>

          {/* ✅ Botão de agendamento */}
          <Button
            size="lg"
            disabled={isLoading || !imageLoaded}
            onClick={() => {
              if (!isLoading && imageLoaded && selectedService) {
                if (selectedService.subcategorySlug && selectedService.slug) {
                  const checkoutUrl = `/servicos/${selectedService.subcategorySlug}/${selectedService.slug}/checkout`;
                  handleAddToCart(selectedService);
                  router.push(checkoutUrl);
                } else {
                  console.error(
                    'Cannot proceed to checkout: subcategorySlug or serviceSlug is missing from selectedService',
                    selectedService
                  );
                  // Optionally, display an error message to the user here
                }
              }
            }}
            className={`flex w-full items-center justify-center rounded-xl bg-gray-900 p-7 py-6 font-bold text-white transition-colors duration-200 hover:bg-gray-800 disabled:cursor-not-allowed disabled:bg-gray-200 disabled:text-gray-500 disabled:hover:bg-gray-200 sm:w-auto`}
          >
            <Calendar className="mr-2 h-4 w-4" aria-hidden="true" />
            Agendar agora
          </Button>
        </div>

        {/* ✅ Imagem do serviço */}
        <div
          key={selectedService.slug} // força recriação quando troca o serviço
          className="relative mb-6 h-56 sm:h-64 md:h-80 lg:mb-0 lg:h-auto lg:min-h-[400px] lg:w-1/2"
          tabIndex={0}
          aria-label={`Imagem ilustrativa do serviço ${selectedService.name}`}
        >
          {!imageLoaded && (
            <div className="absolute inset-0 z-10 animate-pulse rounded-xl bg-gradient-to-r from-gray-200 to-gray-300 md:rounded-none" />
          )}

          <Image
            key={selectedService.slug} // importante para forçar atualização
            src={sanitizeImageUrl(selectedService.imageUrl)}
            alt={`Imagem do serviço ${selectedService.name}`}
            fill
            sizes="(max-width: 768px) 100vw, 50vw"
            style={{ objectFit: 'cover' }}
            priority={true}
            quality={75}
            className={`h-full w-full transition-opacity duration-300 ease-in ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            } rounded-xl md:rounded-none`}
            onLoad={() => {
              setImageLoaded(true); // isLoading for text is handled by selectedService change
            }}
          />
        </div>
      </div>
    </section>
  );
}
