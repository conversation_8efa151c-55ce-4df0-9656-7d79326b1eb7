'use client';
import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, Icon, Separator } from '@/src/app/_components';

import {
  AlertCircle,
  Calendar,
  Check,
  CheckCircle,
  ChevronRight,
  MapPin,
  Shield,
} from 'lucide-react';

import Image from 'next/image';
import Link from 'next/link';
import { MarkdownRenderer } from './MarkdownRenderer';

import { useHandleAddToCart } from '@/src/app/_hooks';
import { ServiceType } from '@/src/app/_interfaces';
import { formatPrice } from '@/src/app/_utils';

interface ServiceDetailsProps {
  service: ServiceType;
}

export function ServiceDetails({ service }: ServiceDetailsProps) {
  const slug = service.slug;
  const { handleAddToCart } = useHandleAddToCart();

  return (
    <section>
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div className="space-y-8 lg:col-span-2">
          <h2 className="mb-8 text-xl font-bold sm:text-2xl md:text-3xl">Detalhes do serviço</h2>
          <div className="space-y-10">
            {service.provider && (
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-4">
                <div className="sm:col-span-1">
                  <div className="flex items-center gap-4">
                    <Shield className="h-6 w-6 flex-shrink-0 stroke-[2px] text-black" />
                    <h3 className="text-xl font-bold text-[#020618]">Prestador</h3>
                  </div>
                </div>
                <div className="transition-all duration-300 ease-in-out sm:col-span-3">
                  <div className="group -mx-4 rounded-lg px-4 py-4 transition-all duration-300 ease-in-out hover:rounded-xl hover:bg-[#F8FAFC]">
                    <Link
                      href={service.provider.providerUrl || ''}
                      className="flex items-center gap-4 hover:cursor-pointer"
                      target="_blank"
                    >
                      <div className="flex-shrink-0">
                        <Image
                          src={service.provider.imageUrl || ''}
                          alt="Logo do provedor"
                          width={80}
                          height={80}
                          className="rounded-full border border-gray-200 p-2"
                        />
                      </div>
                      <div className="flex flex-col gap-2">
                        <h4 className="text-xl font-bold text-[#020618]">
                          {service.provider.name
                            ? service.provider.name
                                .split(' ')
                                .map(
                                  (word) =>
                                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                                )
                                .join(' ')
                            : 'Prestador'}
                        </h4>
                        <p className="text-base text-[#62748E]">{service.provider.description}</p>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            )}
            <Separator className="border-[#E2E8F0]" />

            {/* Disponibilidade */}
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-4">
              <div className="sm:col-span-1">
                <div className="flex items-center gap-4">
                  <MapPin className="h-6 w-6 flex-shrink-0 stroke-[2px] text-black" />
                  <h3 className="text-xl font-bold text-[#020618]">Disponibilidade</h3>
                </div>
              </div>
              <div className="sm:col-span-3">
                <p className="text-lg text-[#62748E]">
                  Este serviço está disponível nos estados de São Paulo, Rio de Janeiro e Minas
                  Gerais.
                </p>
              </div>
            </div>
            <Separator className="border-[#E2E8F0]" />

            {/* Condições */}
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-4">
              <div className="sm:col-span-1">
                <div className="flex items-center gap-4 pt-4">
                  <CheckCircle className="h-6 w-6 flex-shrink-0 stroke-[2px] text-black" />
                  <h3 className="text-xl font-bold text-[#020618]">Condições</h3>
                </div>
              </div>
              <div className="sm:col-span-3">
                {/* Service conditions content */}
                <div className="flex flex-col gap-6">
                  {/* O que está incluso */}
                  <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                    <h5 className="text-lg font-semibold text-[#020618]">O que está incluso</h5>
                    <div className="text-base leading-7 text-[#62748E]">
                      <MarkdownRenderer markdown={service.details} />
                    </div>

                    {/* Alert box for important information */}
                    <div className="rounded-lg bg-slate-50 p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex h-5 w-5 items-center justify-center">
                          <AlertCircle className="h-5 w-5 text-[#62748E]" />
                        </div>
                        <div className="flex-1">
                          <p className="text-base font-medium leading-6 text-[#62748E]">
                            A eventual aquisição das peças é de responsabilidade do cliente, não
                            inclusas no valor.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Restrições */}
                  <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                    <h5 className="text-lg font-semibold text-[#020618]">Restrições</h5>
                    <div className="text-base leading-7 text-[#62748E]">
                      <MarkdownRenderer markdown={service.serviceLimits} />
                    </div>
                  </div>

                  {/* Preparação */}
                  <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                    <h5 className="text-lg font-semibold text-[#020618]">Preparação</h5>
                    <div className="text-base leading-7 text-[#62748E]">
                      <MarkdownRenderer markdown={service.preparations} />
                    </div>
                  </div>
                </div>

                {/* Terms and conditions link */}
                <div className="flex items-center gap-2 py-4">
                  <Link
                    href={service.termsConditionsUrl || ''}
                    target="_blank"
                    className="flex items-center gap-2 text-base font-semibold text-[#62748E] hover:text-[#020618]"
                  >
                    Ver condições gerais
                    <Icon name="ExternalLink" className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Resumo do serviço */}
        <div>
          <Card className="sticky top-24 mt-16 border border-gray-200 bg-white shadow-lg lg:mt-0">
            <CardContent className="p-8">
              <h3 className="mb-8 text-xl font-bold">Resumo do serviço</h3>

              {/* Categoria, subcategoria e tipo de serviço */}
              <div className="mb-8 rounded-lg bg-slate-50 p-6">
                <p className="text-sm font-semibold text-slate-500">
                  {service.categoryName || 'Assistência técnica'}
                </p>
                {/* Mostrar subcategoria apenas se for diferente da categoria */}
                {service.subcategoryName && service.subcategoryName !== service.categoryName ? (
                  <p className="text-lg font-semibold text-slate-900">{service.subcategoryName}</p>
                ) : null}
                <div className="mt-2 flex items-center">
                  <ChevronRight className="text-gray-400" />
                  <p className="font-semibold text-slate-500">{service.name}</p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-3">
                  <Check className="mt-1 h-5 w-5 flex-shrink-0 text-[#FCC800]" strokeWidth={4} />
                  <div>
                    <span className="font-semibold">90 dias de garantia</span>
                    <p className="text-sm text-muted-foreground">
                      Garantia de 90 dias em todos os serviços realizados
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Check className="mt-1 h-5 w-5 flex-shrink-0 text-[#FCC800]" strokeWidth={4} />
                  <div>
                    <span className="font-semibold">Agendamento imediato</span>
                    <p className="text-sm text-muted-foreground">
                      Escolha o melhor dia e a hora do seu atendimento com total segurança e
                      praticidade.
                    </p>
                  </div>
                </div>
              </div>
              <Separator className="my-6 border-gray-200" />
              <div>
                <div className="mb-2">
                  <p className="text-xl font-bold text-muted-foreground line-through">
                    {formatPrice(service.price?.originalPrice || 0, 'BRL')}
                  </p>
                </div>
                <p className="text-3xl font-bold text-primary">
                  {formatPrice(service.price?.finalPrice || 0, 'BRL')}
                </p>
                <Button
                  asChild
                  className="mt-8 w-full rounded-xl bg-primary font-bold hover:bg-primary/90"
                  size="lg"
                  onClick={() => handleAddToCart(service)}
                >
                  <Link href={`${window.location.pathname}/${slug}/checkout`}>
                    <Calendar className="mr-2 h-5 w-5" />
                    Agendar agora
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
