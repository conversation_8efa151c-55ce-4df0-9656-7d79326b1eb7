'use client';

import {
  AddressForm,
  Checkbox,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Icon,
  PersonalInfoForm,
  ScheduleForm,
  ServiceSummaryCard,
} from '@/src/app/_components';

import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { useHandleBeginCheckout } from '@/src/app/_hooks/';
import type { CheckoutFormProps } from '@/src/app/_interfaces';
import { CheckoutFormSchema, checkoutFormSchema } from '@/src/app/_utils';
import { MarkdownRenderer } from '../Service/MarkdownRenderer';

export function CheckoutForm({ service, onSubmit }: CheckoutFormProps) {
  // UI state
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  const [isStateSelectVisible, setIsStateSelectOpen] = useState(false);
  const [focusedBlock, setFocusedBlock] = useState<string | null>(null);
  const [formIsValid, setFormIsValid] = useState(false);

  const { trackBeginCheckout } = useHandleBeginCheckout();

  const methods = useForm<CheckoutFormSchema>({
    resolver: zodResolver(checkoutFormSchema),
    mode: 'onChange',
    defaultValues: {
      firstName: '',
      lastName: '',
      countryCode: '+55', // Default to Brazil
      phone: '',
      cpf: '',
      email: '',
      cep: '',
      street: '',
      streetNumber: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      date: undefined,
      period: undefined,
      terms: false,
    },
  });

  // Set default country code
  useEffect(() => {
    methods.setValue('countryCode', '+55');
    methods.trigger('countryCode');
  }, [methods]);

  // Add effect to watch form field changes and update formIsValid
  useEffect(() => {
    // Watch all form fields including the terms checkbox
    const subscription = methods.watch((_, { name }) => {
      // Force validation when terms checkbox changes
      if (name === 'terms') {
        methods.trigger();
      }
      // Update form validity whenever anything changes
      setFormIsValid(isFormValid());
    });

    // Clean up subscription
    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [methods]);

  // Add effect to watch form state changes (errors, isDirty, etc.)
  useEffect(() => {
    // Update form validity when form state changes
    setFormIsValid(isFormValid());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [methods.formState]);

  // Form validation
  const isFormValid = () => {
    const values = methods.getValues();
    const {
      firstName,
      lastName,
      date,
      period,
      cep,
      street,
      streetNumber,
      neighborhood,
      countryCode,
      city,
      state,
      phone,
      cpf,
      email,
      terms,
    } = values;

    // Check if all required fields are filled
    const allFieldsFilled =
      !!firstName &&
      !!lastName &&
      !!date &&
      !!period &&
      !!cep &&
      !!street &&
      !!countryCode &&
      !!streetNumber &&
      !!neighborhood &&
      !!city &&
      !!state &&
      !!phone &&
      !!cpf &&
      !!email &&
      !!terms;

    // Ensure there are no validation errors
    const noValidationErrors = Object.keys(methods.formState.errors).length === 0;

    // Check if the form is valid
    const isValid = allFieldsFilled && noValidationErrors;

    // For debugging
    if (!isValid) {
      // Commented out to avoid console logs
      // console.log('Form validation failed:', {
      //   allFieldsFilled,
      //   noValidationErrors,
      //   errors: methods.formState.errors,
      //   terms,
      // });
    }

    return isValid;
  };

  const handleBeginCheckout = async () => {
    // Validate the form before proceeding
    const isValid = await methods.trigger();
    setFormIsValid(isFormValid());

    if (isValid) {
      //@ts-expect-error
      trackBeginCheckout(service);
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="flex flex-col gap-12 md:grid md:grid-cols-3">
        <div className="md:order-1 md:col-span-2">
          <div>
            <h1 className="mb-8 text-2xl font-bold">Agendar serviço</h1>
            <form
              id="checkout-form"
              onSubmit={methods.handleSubmit(onSubmit)}
              className="space-y-12 px-0"
            >
              <ScheduleForm
                isDatePickerVisible={isDatePickerVisible}
                setIsDatePickerVisible={setIsDatePickerVisible}
                focusedBlock={focusedBlock}
                setFocusedBlock={setFocusedBlock}
              />

              <AddressForm
                service={service}
                isStateSelectVisible={isStateSelectVisible}
                setIsStateSelectOpen={setIsStateSelectOpen}
                focusedBlock={focusedBlock}
                setFocusedBlock={setFocusedBlock}
              />

              <PersonalInfoForm focusedBlock={focusedBlock} setFocusedBlock={setFocusedBlock} />

              <div className="w-full overflow-hidden md:hidden">
                <ServiceSummaryCard
                  service={service}
                  onSubmit={handleBeginCheckout}
                  isFormValid={formIsValid}
                  showButton={true}
                  formId="checkout-form"
                />
              </div>

              <FormField
                control={methods.control}
                name="terms"
                render={({ field }) => (
                  <FormItem className="flex flex-col rounded-xl border border-slate-200 bg-white p-6 shadow-sm">
                    {/* Header with check icon and title */}
                    <div className="mb-6 flex items-center gap-4">
                      <div className="flex h-5 w-5 items-center justify-center">
                        <Icon name="CheckCircle" className="h-5 w-5 text-black" />
                      </div>
                      <h4 className="text-xl font-bold leading-7 text-[#020618]">
                        Você está de acordo com as condições do serviço?
                      </h4>
                    </div>

                    {/* Service conditions content */}
                    <div className="flex flex-col gap-6">
                      {/* O que está incluso */}
                      <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                        <h5 className="text-base font-semibold text-[#020618]">
                          O que está incluso
                        </h5>
                        <div className="text-base leading-7 text-[#62748E]">
                          <MarkdownRenderer markdown={service.details} />
                        </div>

                        {/* Alert box for important information */}
                        <div className="rounded-lg bg-slate-50 p-4">
                          <div className="flex items-start gap-4">
                            <div className="flex h-5 w-5 items-center justify-center">
                              <Icon name="AlertCircle" className="h-5 w-5 text-[#62748E]" />
                            </div>
                            <div className="flex-1">
                              <p className="text-base font-medium leading-6 text-[#62748E]">
                                A eventual aquisição das peças é de responsabilidade do cliente, não
                                inclusas no valor.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Restrições */}
                      <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                        <h5 className="text-base font-semibold text-[#020618]">Restrições</h5>
                        <div className="text-base leading-7 text-[#62748E]">
                          <MarkdownRenderer markdown={service.serviceLimits} />
                        </div>
                      </div>

                      {/* Preparação */}
                      <div className="flex flex-col gap-4 border-b border-slate-200 pb-4">
                        <h5 className="text-base font-semibold text-[#020618]">Preparação</h5>
                        <div className="text-base leading-7 text-[#62748E]">
                          <MarkdownRenderer markdown={service.preparations} />
                        </div>
                      </div>
                    </div>

                    {/* Terms and conditions link */}
                    <div className="flex items-center gap-2 py-4">
                      <Link
                        href={service.termsConditionsUrl || ''}
                        target="_blank"
                        className="flex items-center gap-2 text-base font-semibold text-[#62748E] hover:text-[#020618]"
                      >
                        Ver condições gerais
                        <Icon name="ExternalLink" className="h-4 w-4" />
                      </Link>
                    </div>

                    {/* Checkbox */}
                    <div className="flex items-start gap-3">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            // Trigger validation for terms and update form validity
                            methods.trigger('terms').then(() => {
                              setFormIsValid(isFormValid());
                            });
                          }}
                          required
                          id="terms-checkbox"
                          className="mt-1 h-4 w-4 rounded border border-[#020618] data-[state=checked]:bg-[#020618] data-[state=checked]:text-white"
                        />
                      </FormControl>
                      <div className="flex flex-col">
                        <label
                          htmlFor="terms-checkbox"
                          className="text-base font-medium leading-6 text-black"
                        >
                          Confirmo que estou de acordo com as condições descritas acima.
                        </label>
                        <FormMessage />
                      </div>
                    </div>
                  </FormItem>
                )}
              />

              {/* Button moved to ServiceSummaryCard */}
            </form>
          </div>
        </div>

        <div className="hidden md:order-2 md:col-span-1 md:block">
          <div className="sticky top-28">
            <ServiceSummaryCard
              service={service}
              onSubmit={handleBeginCheckout}
              isFormValid={formIsValid}
              showButton={true}
              formId="checkout-form"
            />
          </div>
        </div>
      </div>
    </FormProvider>
  );
}
