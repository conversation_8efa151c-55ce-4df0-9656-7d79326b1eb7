/**
 * @jest-environment jsdom
 */

import { ServiceDetails } from '@/src/app/_components/Pages/Service/ServiceDetails';
import { ServiceType } from '@/src/app/_interfaces';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

// Mock the analytics hook
jest.mock('@/src/app/_hooks', () => ({
  useHandleAddToCart: jest.fn().mockReturnValue({
    handleAddToCart: jest.fn(),
  }),
}));

// Mock formatPrice utility
jest.mock('@/src/app/_utils', () => ({
  formatPrice: jest.fn((price, _currency = 'BRL') => {
    if (price === null || price === undefined) {
      return 'R$ 0,00';
    }
    return `R$ ${price.toFixed(2)}`;
  }),
}));

// Mock components
jest.mock('@/src/app/_components', () => ({
  Button: jest.fn(({ children, onClick, asChild, className, size }) => (
    <button
      data-testid="button"
      onClick={onClick}
      data-aschild={asChild ? 'true' : 'false'}
      className={className}
      data-size={size}
    >
      {children}
    </button>
  )),
  Card: jest.fn(({ children, className }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  )),
  CardContent: jest.fn(({ children, className }) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  )),
  Icon: jest.fn(({ name, className }) => (
    <span data-testid={`icon-${name}`} className={className} role="img" aria-label={name}>
      {name}
    </span>
  )),
  Separator: jest.fn(({ className }) => <hr data-testid="separator" className={className} />),
}));

// Mock Next/Image
jest.mock('next/image', () => ({
  __esModule: true,
  default: jest.fn(({ src, alt, ...props }) => (
    <img data-testid="next-image" src={src} alt={alt} {...props} />
  )),
}));

// Mock Next/Link
jest.mock('next/link', () => ({
  __esModule: true,
  default: jest.fn(({ href, children, ...props }) => (
    <a data-testid="next-link" href={href} {...props}>
      {children}
    </a>
  )),
}));

// Mock Lucide icons
jest.mock('lucide-react', () => ({
  AlertCircle: jest.fn((props) => <span data-testid="alert-circle-icon" {...props} />),
  Ban: jest.fn((props) => <span data-testid="ban-icon" {...props} />),
  Calendar: jest.fn((props) => <span data-testid="calendar-icon" {...props} />),
  Check: jest.fn((props) => <span data-testid="check-icon" {...props} />),
  CheckCircle: jest.fn((props) => <span data-testid="check-circle-icon" {...props} />),
  ChevronRight: jest.fn((props) => <span data-testid="chevron-right-icon" {...props} />),
  ListTodo: jest.fn((props) => <span data-testid="list-todo-icon" {...props} />),
  MapPin: jest.fn((props) => <span data-testid="map-pin-icon" {...props} />),
  Shield: jest.fn((props) => <span data-testid="shield-icon" {...props} />),
}));

// Mock MarkdownRenderer
jest.mock('@/src/app/_components/Pages/Service/MarkdownRenderer', () => ({
  MarkdownRenderer: jest.fn(({ markdown, ...props }) => (
    <div data-testid="markdown-renderer" {...props}>
      {markdown}
    </div>
  )),
}));

describe('ServiceDetails', () => {
  const mockService: ServiceType = {
    id: 123,
    name: 'Test Service',
    slug: 'test-service',
    description: 'Test service description',
    imageUrl: '/images/test-service.jpg',
    status: 'active',
    price: {
      priceId: 1,
      originalPrice: 150,
      discountPrice: 120,
      finalPrice: 120,
    },
    provider: {
      id: 456,
      name: 'Test Provider',
      imageUrl: '/images/test-provider.jpg',
      providerUrl: '/providers/test-provider',
      description: 'A reliable provider',
    },
    availableIn: ['São Paulo', 'Rio de Janeiro'],
    details: ['Detail 1', 'Detail 2'],
    serviceLimits: 'Some limitations apply',
    keywords: ['test', 'service'],
    termsConditionsUrl: '/terms',
    preparations: 'Prepare by doing this and that',
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
  };

  beforeEach(() => {
    // Mock window.location.pathname
    Object.defineProperty(window, 'location', {
      value: {
        ...window.location,
        pathname: '/servicos/test-subcategory',
      },
      writable: true,
    });

    jest.clearAllMocks();
  });

  it('renders the component without errors', () => {
    render(<ServiceDetails service={mockService} />);

    // Check for section headers
    expect(screen.getByText('Detalhes do serviço')).toBeInTheDocument();
    expect(screen.getByText('Prestador')).toBeInTheDocument();
    expect(screen.getByText('Disponibilidade')).toBeInTheDocument();
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();

    // Check for provider details
    expect(screen.getByText('Test Provider')).toBeInTheDocument();
    expect(screen.getByText('A reliable provider')).toBeInTheDocument();

    // Check for static content sections (no accordion)
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();

    // Check for alert box with warning text
    expect(
      screen.getByText(
        'A eventual aquisição das peças é de responsabilidade do cliente, e não estão inclusas no valor.'
      )
    ).toBeInTheDocument();
    expect(screen.getByTestId('alert-circle-icon')).toBeInTheDocument();

    // Check for terms and conditions link
    expect(screen.getByText('Ver condições gerais')).toBeInTheDocument();

    // Check for price information
    expect(screen.getByText('R$ 150.00')).toBeInTheDocument();
    expect(screen.getByText('R$ 120.00')).toBeInTheDocument();

    // Check for button
    expect(screen.getByText('Agendar agora')).toBeInTheDocument();
  });

  it('calls handleAddToCart when button is clicked', () => {
    // Get the mocked function
    const { useHandleAddToCart } = require('@/src/app/_hooks');
    const mockHandleAddToCart = jest.fn();

    // Update the mock implementation for this test
    (useHandleAddToCart as jest.Mock).mockReturnValue({
      handleAddToCart: mockHandleAddToCart,
    });

    render(<ServiceDetails service={mockService} />);

    // Find and click the button
    const button = screen.getByTestId('button');
    fireEvent.click(button);

    // Verify that handleAddToCart was called with the service
    expect(mockHandleAddToCart).toHaveBeenCalledWith(mockService);
  });

  it('handles service without provider correctly', () => {
    const serviceWithoutProvider = {
      ...mockService,
      provider: undefined,
    };

    render(<ServiceDetails service={serviceWithoutProvider} />);

    // Provider section should not be rendered
    expect(screen.queryByText('Prestador')).not.toBeInTheDocument();

    // Other sections should still be rendered
    expect(screen.getByText('Detalhes do serviço')).toBeInTheDocument();
    expect(screen.getByText('Disponibilidade')).toBeInTheDocument();
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
  });

  it('handles service with minimal provider correctly', () => {
    const serviceWithMinimalProvider = {
      ...mockService,
      provider: {
        id: 1,
        name: 'Test Provider',
        imageUrl: '',
        providerUrl: '',
        description: '',
      },
    };

    render(<ServiceDetails service={serviceWithMinimalProvider} />);

    // Provider section should be rendered
    expect(screen.getByText('Prestador')).toBeInTheDocument();
    expect(screen.getByText('Test Provider')).toBeInTheDocument();

    // Empty description should not cause issues
    expect(screen.queryByText('undefined')).not.toBeInTheDocument();
  });

  it('handles service without terms and conditions URL correctly', () => {
    const serviceWithoutTerms = {
      ...mockService,
      termsConditionsUrl: '',
    };

    render(<ServiceDetails service={serviceWithoutTerms} />);

    // Terms and conditions link should still be rendered but with empty href
    const termsLink = screen.getByText('Ver condições gerais');
    expect(termsLink).toBeInTheDocument();

    // Find the specific link containing the terms text
    const termsLinkElement = screen.getByText('Ver condições gerais').closest('a');
    expect(termsLinkElement).toHaveAttribute('href', '');
  });

  it('handles service with same category and subcategory name', () => {
    const serviceWithSameNames = {
      ...mockService,
      categoryName: 'Same Name',
      subcategoryName: 'Same Name',
    };

    render(<ServiceDetails service={serviceWithSameNames} />);

    // Category should be displayed
    expect(screen.getByText('Same Name')).toBeInTheDocument();

    // Subcategory should not be displayed since it's the same as category
    const subcategoryElements = screen.getAllByText('Same Name');
    expect(subcategoryElements.length).toBe(1); // Only one instance should be found
  });

  it('handles service without subcategory name', () => {
    const serviceWithoutSubcategory = {
      ...mockService,
      subcategoryName: undefined,
    };

    render(<ServiceDetails service={serviceWithoutSubcategory} />);

    // Category should be displayed
    expect(screen.getByText('Test Category')).toBeInTheDocument();

    // No subcategory should be displayed
    expect(screen.queryByTestId('subcategory')).not.toBeInTheDocument();
  });

  it('handles service without category name', () => {
    const serviceWithoutCategory = {
      ...mockService,
      categoryName: undefined,
    };

    render(<ServiceDetails service={serviceWithoutCategory} />);

    // Default category should be displayed
    expect(screen.getByText('Assistência técnica')).toBeInTheDocument();

    // Subcategory should still be displayed
    expect(screen.getByText('Test Subcategory')).toBeInTheDocument();
  });

  it('handles service without image URL', () => {
    const serviceWithoutImage = {
      ...mockService,
      provider: {
        ...mockService.provider!,
        imageUrl: '',
      },
    };

    render(<ServiceDetails service={serviceWithoutImage} />);

    // Component should render without errors
    expect(screen.getByText('Test Provider')).toBeInTheDocument();

    // Just verify the component renders without errors
    // We don't need to check the src attribute since it's handled by Next.js Image component
    const image = screen.getByAltText('Logo do provedor');
    expect(image).toBeInTheDocument();
  });

  it('handles service with null provider values', () => {
    const serviceWithNullProvider = {
      ...mockService,
      provider: {
        ...mockService.provider!,
        name: null,
        description: null,
        imageUrl: null,
        providerUrl: null,
      },
    };

    // This should not throw an error
    render(<ServiceDetails service={serviceWithNullProvider as any} />);

    // Component should render without errors
    // Use getAllByText since there might be multiple elements with "Prestador" text
    const prestadorElements = screen.getAllByText('Prestador');
    expect(prestadorElements.length).toBeGreaterThan(0);

    // Verify the provider section is rendered
    const providerHeading = screen.getAllByText('Prestador').find((el) => el.tagName === 'H3');
    expect(providerHeading).toBeInTheDocument();
  });

  it('handles service with null price values', () => {
    const serviceWithNullPrice = {
      ...mockService,
      price: {
        ...mockService.price,
        originalPrice: null,
        finalPrice: null,
      },
    };

    // This should not throw an error
    render(<ServiceDetails service={serviceWithNullPrice as any} />);

    // Component should render without errors
    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();
  });

  it('handles service with null markdown content', () => {
    const serviceWithNullMarkdown = {
      ...mockService,
      details: null,
      preparations: null,
      serviceLimits: null,
    };

    // This should not throw an error
    render(<ServiceDetails service={serviceWithNullMarkdown as any} />);

    // Component should render without errors
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByText('Restrições')).toBeInTheDocument();
  });

  it('renders separate main sections with proper icons', () => {
    render(<ServiceDetails service={mockService} />);

    // Check that each section is a separate main section with proper icons
    expect(screen.getByText('O que está incluso')).toBeInTheDocument();
    expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument();

    expect(screen.getByText('Restrições')).toBeInTheDocument();
    expect(screen.getByTestId('ban-icon')).toBeInTheDocument();

    expect(screen.getByText('Preparação')).toBeInTheDocument();
    expect(screen.getByTestId('list-todo-icon')).toBeInTheDocument();

    // Check that service details are visible without interaction
    // The MarkdownRenderer renders content as a block, so we check for the rendered content
    const markdownRenderers = screen.getAllByTestId('markdown-renderer');
    expect(markdownRenderers.length).toBe(3); // One for each section: details, serviceLimits, preparations

    // Check that the content is rendered in the markdown renderers
    expect(markdownRenderers[0]).toHaveTextContent('Detail 1Detail 2');
    expect(markdownRenderers[1]).toHaveTextContent('Some limitations apply');
    expect(markdownRenderers[2]).toHaveTextContent('Prepare by doing this and that');

    // Verify no accordion components are rendered
    expect(screen.queryByTestId('accordion')).not.toBeInTheDocument();
    expect(screen.queryByTestId('accordion-trigger')).not.toBeInTheDocument();
    expect(screen.queryByTestId('accordion-content')).not.toBeInTheDocument();
  });

  it('renders alert box with proper styling and accessibility', () => {
    render(<ServiceDetails service={mockService} />);

    // Check for alert box content
    const alertText = screen.getByText(
      'A eventual aquisição das peças é de responsabilidade do cliente, e não estão inclusas no valor.'
    );
    expect(alertText).toBeInTheDocument();

    // Check for alert icon with proper accessibility
    const alertIcon = screen.getByTestId('alert-circle-icon');
    expect(alertIcon).toBeInTheDocument();

    // Check that the alert is within the "O que está incluso" section
    const inclusiveSection = screen.getByText('O que está incluso');
    expect(inclusiveSection).toBeInTheDocument();
  });
});
